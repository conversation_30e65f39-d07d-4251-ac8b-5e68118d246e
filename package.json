{"name": "qyp-mini", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "tsc && vite build && vite preview", "tauri": "tauri", "deploy-web": "pnpm run build && dotenv -e .env.wrangler -- wrangler pages deploy --project-name qyp-mini ./dist"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/oxide": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "~2.3.1", "@tauri-apps/plugin-fs": "~2.4.1", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "~2", "@tauri-apps/plugin-sql": "~2.3.0", "@tauri-apps/plugin-stronghold": "~2.3.0", "@tiny-utils/bytes": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "esbuild-wasm": "^0.25.8", "lightningcss-wasm": "^1.30.1", "lucide-react": "^0.525.0", "mobx": "^6.13.5", "mobx-react": "^9.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-resizable-panels": "^3.0.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-v3": "npm:tailwindcss@^3.4.17"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^24.1.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.11.0", "prettier": "^3.3.3", "tw-animate-css": "^1.3.6", "typescript": "~5.6.2", "vite": "^6.0.3", "vite-plugin-node-polyfills": "^0.24.0", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^3.95.0"}}