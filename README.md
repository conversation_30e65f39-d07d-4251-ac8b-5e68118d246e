<div align="center">

# qYp mini app

[![Telegram Follow](https://img.shields.io/badge/Telegram-blue?style=for-the-badge&logo=telegram&logoColor=white)](https://t.me/elkornacio) [![Twitter Follow](https://img.shields.io/twitter/follow/elkornacio?style=for-the-badge&logo=x&logoColor=white)](https://x.com/elkornacio) [![GitHub Issues](https://img.shields.io/github/issues/elkornacio/qyp-mini?style=for-the-badge&logo=github&logoColor=white)](https://github.com/elkornacio/qyp-mini/issues) [![GitHub Stars](https://img.shields.io/github/stars/elkornacio/qyp-mini?style=for-the-badge&logo=github&logoColor=white)](https://github.com/elkornacio/qyp-mini/stargazers)

🤖 Open-source AI-agent for SQL databases with generative UI. A little brother for [qyp.ai](https://qyp.ai).

<img src="assets/gh-screen.png" alt="qYp-mini screenshot" width="800"/>

</div>

# Tech stack:

- [Tauri](https://tauri.app/) + [Vite](https://vitejs.dev/) + [React](https://react.dev/) + [Typescript](https://www.typescriptlang.org/)
- [Tailwind](https://tailwindcss.com/) + [shadcn/ui](https://ui.shadcn.com/)
- [Cursor](https://www.cursor.com/) + [Supercode.sh](https://supercode.sh/)
<!--stackedit_data:
eyJoaXN0b3J5IjpbLTE4NTg4NjU3MCwtMTg1ODg2NTcwXX0=
-->