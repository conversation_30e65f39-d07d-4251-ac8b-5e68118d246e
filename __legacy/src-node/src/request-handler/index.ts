import { CompileComponentRequest, CompileComponentResponse } from '../types/index.js';
import { createError } from '../utils/index.js';
import { Compiler } from '../compiler/index.js';

/**
 * Обработчик команды компиляции
 */
export class CompileCommandHandler {
	private compiler: Compiler;

	constructor() {
		this.compiler = new Compiler();
	}

	/**
	 * Обрабатывает запрос на компиляцию
	 */
	async handle(request: CompileComponentRequest): Promise<CompileComponentResponse> {
		try {
			if (request.serializedVFS.length === 0) {
				throw createError('Массив файлов пуст');
			}

			// Компиляция
			const result = await this.compiler.compile(request.serializedVFS, request.entryPoint);

			return result;
		} catch (error) {
			throw createError('Ошибка обработки запроса компиляции', error);
		}
	}
}
