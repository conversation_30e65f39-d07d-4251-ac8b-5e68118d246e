{"name": "src-back", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon", "test": "echo \"Error: no test specified\" && exit 1", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\""}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "mysql2": "^3.14.2", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "sqlite3": "^5.1.7", "typeorm": "^0.3.25"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}