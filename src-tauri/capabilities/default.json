{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "core:window:default", "core:window:allow-set-size", "core:window:allow-center", "opener:default", "shell:allow-stdin-write", "shell:default", "sql:default", "sql:allow-execute", "sql:allow-close", "sql:allow-load", "dialog:default", "dialog:allow-confirm", "fs:default", "stronghold:default", {"identifier": "fs:scope", "allow": [{"path": "$APPDATA"}, {"path": "$APPDATA/**/*"}]}, {"identifier": "fs:allow-app-write-recursive", "allow": [{"path": "$APPDATA"}, {"path": "$APPDATA/**/*"}]}]}