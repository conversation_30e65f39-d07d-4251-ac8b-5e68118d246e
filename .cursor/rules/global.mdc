---
alwaysApply: true
---

# qYp-mini Architecture Guidelines

Conversation style: never give any summaries if not explicitly asked. Focus on implementation high-quality code.

If user gave you a task, this task is considered completed only if you've called `pnpm run build` and it worked without any errors.

## Project Overview

qYp-mini is an AI-agent for SQL databases with generative UI capabilities, built as a desktop application using Tauri. The application enables users to interact with databases through natural language, generating SQL queries and React components dynamically.

## Architecture

### Technology Stack

- **Package Manager**: pnpm
- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS v4, shadcn/ui
- **Desktop Framework**: Tauri v2 (Rust-based)
- **Backend**:
    - Tauri Rust backend for native OS integration
    - Node.js TypeScript backend (compiled to self-executable binary)
- **UI Components**: shadcn/ui with Radix UI primitives

### Core Principles

- **Separation of Concerns**: Clear boundaries between UI, business logic, and data layers
- **Plugin Architecture**: Extensible system for database adapters and UI components
- **Security First**: Sandboxed execution for generated components
- **Modular Design**: Loosely coupled modules for maintainability

### Directory Structure

```
qyp-mini/                         # Root directory, we are here
├── src/                          # Frontend React application
│   ├── components/
│   │   └── ui/                   # Base UI components (shadcn)
│   │       ├── badge.tsx
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── input.tsx
│   │       ├── resizable-panel.tsx
│   │       ├── tabs.tsx
│   │       └── textarea.tsx
│   ├── pages/                    # Application pages
│   │   ├── PlaygroundPage.tsx    # Main playground interface
│   │   ├── WelcomePage.tsx       # Welcome/landing page
│   │   └── WidgetsLibraryPage.tsx # Widgets library page
│   ├── lib/                      # Shared utilities and core functionality
│   │   ├── compiler/             # Component compilation system
│   │   ├── runtime/              # Runtime execution system
│   │   └── utils.ts              # General utilities
│   ├── pipeline/                 # Build and validation pipeline
│   │   ├── ValidationStage.ts    # Validation stage implementation
│   │   └── modules-mocks/        # Mock modules for compilation
│   ├── stores/                   # Global state management
│   │   └── WidgetsLibrary.ts     # Widget library state store
│   ├── types/                    # TypeScript definitions
│   ├── virtual-fs/               # Virtual file system
│   ├── assets/                   # Static assets
│   ├── App.tsx                   # Main application component
│   ├── main.tsx                  # Application entry point
│   └── index.css                 # Global styles
├── src-tauri/                    # Tauri Rust backend (managed by Tauri, don't touch)
```

### Key Components

#### Compiler System (`src/lib/compiler/`)

- **ComponentCompiler.ts**: Основной интерфейс компилятора для компонентов
- **ESBuildCompiler.ts**: Компилятор на основе ESBuild для сборки TypeScript/JSX
- **ESBuildVFS.ts**: Виртуальная файловая система для ESBuild
- **TailwindCompiler.ts**: Компилятор для обработки Tailwind CSS

#### Runtime System (`src/lib/runtime/`)

- **ComponentRuntime.ts**: Система выполнения скомпилированных компонентов

#### Virtual File System (`src/virtual-fs/`)

- **VirtualFS.ts**: Основная реализация виртуальной файловой системы
- **default-fs.ts**: Настройка файловой системы по умолчанию
- **shadcn-ui.ts**: Виртуальные файлы для компонентов shadcn/ui

#### State Management (`src/stores/`)

- **WidgetsLibrary.ts**: Управление состоянием библиотеки виджетов

#### Pages (`src/pages/`)

- **PlaygroundPage.tsx**: Основная страница для тестирования и разработки
- **WelcomePage.tsx**: Страница приветствия
- **WidgetsLibraryPage.tsx**: Страница библиотеки виджетов

### Development Guidelines

1. **Состояние приложения**: Используйте централизованные stores в `src/stores/` для управления состоянием
2. **Типизация**: Определяйте типы в `src/types/` для обеспечения type safety

### Coding Standards

- Все комментарии в коде должны быть на русском языке
- Используйте TypeScript для всех новых файлов
- Следуйте принципам модульной архитектуры
- Применяйте паттерны проектирования для поддержания чистоты кода

# qYp-mini Architecture Guidelines

## Project Overview

qYp-mini is an AI-agent for SQL databases with generative UI capabilities, built as a desktop application using Tauri. The application enables users to interact with databases through natural language, generating SQL queries and React components dynamically.

## Architecture

### Technology Stack

- **Package Manager**: pnpm
- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS v4, shadcn/ui
- **Desktop Framework**: Tauri v2 (Rust-based)
- **Backend**:
    - Tauri Rust backend for native OS integration
    - Node.js TypeScript backend (compiled to self-executable binary)
- **UI Components**: shadcn/ui with Radix UI primitives

### Core Principles

- **Separation of Concerns**: Clear boundaries between UI, business logic, and data layers
- **Plugin Architecture**: Extensible system for database adapters and UI components
- **Security First**: Sandboxed execution for generated components
- **Modular Design**: Loosely coupled modules for maintainability

### Directory Structure

```
qyp-mini/                         # Root directory, we are here
├── src/                          # Frontend React application
│   ├── components/
│   │   └── ui/                   # Base UI components (shadcn)
│   │       ├── badge.tsx
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── input.tsx
│   │       ├── resizable-panel.tsx
│   │       ├── tabs.tsx
│   │       └── textarea.tsx
│   ├── pages/                    # Application pages
│   │   ├── PlaygroundPage.tsx    # Main playground interface
│   │   ├── WelcomePage.tsx       # Welcome/landing page
│   │   └── WidgetsLibraryPage.tsx # Widgets library page
│   ├── lib/                      # Shared utilities and core functionality
│   │   ├── compiler/             # Component compilation system
│   │   ├── runtime/              # Runtime execution system
│   │   └── utils.ts              # General utilities
│   ├── pipeline/                 # Build and validation pipeline
│   │   ├── ValidationStage.ts    # Validation stage implementation
│   │   └── modules-mocks/        # Mock modules for compilation
│   ├── stores/                   # Global state management
│   │   └── WidgetsLibrary.ts     # Widget library state store
│   ├── types/                    # TypeScript definitions
│   ├── virtual-fs/               # Virtual file system
│   ├── assets/                   # Static assets
│   ├── App.tsx                   # Main application component
│   ├── main.tsx                  # Application entry point
│   └── index.css                 # Global styles
├── src-tauri/                    # Tauri Rust backend (managed by Tauri, don't touch)
```

### Key Components

#### Compiler System (`src/lib/compiler/`)

- **ComponentCompiler.ts**: Основной интерфейс компилятора для компонентов
- **ESBuildCompiler.ts**: Компилятор на основе ESBuild для сборки TypeScript/JSX
- **ESBuildVFS.ts**: Виртуальная файловая система для ESBuild
- **TailwindCompiler.ts**: Компилятор для обработки Tailwind CSS

#### Runtime System (`src/lib/runtime/`)

- **ComponentRuntime.ts**: Система выполнения скомпилированных компонентов

#### Virtual File System (`src/virtual-fs/`)

- **VirtualFS.ts**: Основная реализация виртуальной файловой системы
- **default-fs.ts**: Настройка файловой системы по умолчанию
- **shadcn-ui.ts**: Виртуальные файлы для компонентов shadcn/ui

#### State Management (`src/stores/`)

- **WidgetsLibrary.ts**: Управление состоянием библиотеки виджетов

#### Pages (`src/pages/`)

- **PlaygroundPage.tsx**: Основная страница для тестирования и разработки
- **WelcomePage.tsx**: Страница приветствия
- **WidgetsLibraryPage.tsx**: Страница библиотеки виджетов

### Development Guidelines

1. **Состояние приложения**: Используйте централизованные stores в `src/stores/` для управления состоянием
2. **Типизация**: Определяйте типы в `src/types/` для обеспечения type safety

### Coding Standards

- Все комментарии в коде должны быть на русском языке
- Используйте TypeScript для всех новых файлов
- Следуйте принципам модульной архитектуры
- Применяйте паттерны проектирования для поддержания чистоты кода
